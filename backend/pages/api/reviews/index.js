import { allowCORS } from "@/@common-utils/allowCORS";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { getWP, getWPFilterORM, postWP, putWP } from "@/@common-utils/callWP";
import { sendNoti } from "@/@common-utils/sendNoti";
import { updateUserReviewSummary } from "@/@common-utils/syncReviews";

export const reviewApi = async (req, res) => {
	try {
		const user = await checkFirebaseAuth(req);
		if (!user || !user.email)
			return res.json({
				success: false,
				code: 403,
				message: "You need to login first.",
			});
		await allowCORS(req, res);
		if (req.method === "GET") {
      const userId = req.query.userId;

			const reviews = await getWP(
				`/reviews?filter[meta_query][relation]=AND&filter[meta_query][0][key]=review_recipient&filter[meta_query][0][value]=${userId}`
			);
			if (!reviews) {
				res.json({
					success: false,
					code: 500,
					message: "Failed to get reviews.",
				});
				return;
			}
			res.json({
				success: true,
				data: reviews,
			});
		}
		if (req.method === "POST") {
			const { rate_number, user_review, review_text, review_recipient, application_id, job_id } = req.body;
			console.log('req.body', req.body);
			const reviews = await postWP(`/reviews`, {
				rate_number,
				user_review,
				review_text,
				review_recipient,
				title: review_text.length < 100 ? review_text : review_text.substring(0, 100) + "...",
				status: "publish",
				review_read_status: "unread",
			});
			if (!reviews) {
				res.json({
					success: false,
					code: 500,
					message: "Failed to get reviews.",
				});
				return;
			}

			// NEW: Sync review summary to user table (non-blocking)
			if (reviews && !reviews.code && review_recipient) {
				updateUserReviewSummary(review_recipient).catch(error => {
					console.error('Failed to sync review summary:', error);
					// Don't fail the request if sync fails
				});
			}

			var job;
			var dbUser;

			if (application_id) {
				try {
					job = await getWP(`/job_application/${application_id}`);
					dbUser = await getWPFilterORM(`/app_user?_fields=id,email,user_name`, { email: user.email });
					console.log('dbUser', dbUser);
					let businessEmail = job.business[0].email;
					if (!businessEmail) throw new Error("Business email not found");

					await putWP(`/job_application/${application_id}`, {
						is_left_review_from_trade: "yes",
						...(job_id ? { job_status: "Completed" } : {}),
					});

					const messageData = {
						title: "New Review",
						text: `There is a new review from ${dbUser[0].user_name} on your application.`,
						html: `There is a new review from ${dbUser[0].user_name} on your application.`,
					};

					await sendNoti(
						businessEmail,
						messageData,
						{
							mobile: true,
							mail: false,
						},
					);
				} catch (e) {
					console.error(
						"Failed to get business email while sending notification on application_id:",
						application_id,
						e
					);
				}
			}
			if (job_id) {
				try {
					const updatedJob = await putWP(`/jobs/${job_id}`, {
						job_status: "Completed",
					});
					if (updatedJob.code) {
						throw new Error("Failed to update job status", updatedJob);
					}
				} catch (e) {
					console.error("Failed to update job status:", job_id, e);
					res.json({
						success: false,
						code: 500,
						message: "Failed to update job status",
					});
					return;
				}
			}
			res.json({
				success: true,
				data: reviews,
				job,
			});
		}
	} catch (error) {
		console.log("error", error);
	}
};

export default reviewApi;
