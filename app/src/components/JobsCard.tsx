import { Colors } from 'react-native/Libraries/NewAppScreen';
import { View, Text, TouchableOpacity } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import CustomText from './CustomText';
import StarRating from 'react-native-star-rating';
import FontistoIcons from 'react-native-vector-icons/Fontisto';
import { useNavigation } from '@react-navigation/native';

export default function JobsCard({
  name,
  title,
  date,
  location,
  isGreen = false,
  showAvatar = true,
  onPress,
  company,
  style = {},
  isExpired = false,
  reviewedRating = 0,
  reviewedNumber = 0,
  isFavorite = false,
}) {
  const listItem = {
    backgroundColor: isExpired ? '#ccc' : Colors.white,
    borderRadius: 10,
    padding: 20,
    flexDirection: 'row',
  };

  const favoriteListItem = {
    backgroundColor: '#FFF9E6',
    borderRadius: 10,
    padding: 20,
    flexDirection: 'row',
    borderWidth: 3,
    borderColor: '#FFD700',
    shadowColor: '#FFD700',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  };

  const icon = {
    width: 50,
    height: 50,
    marginRight: 15,
  };

  const textStyle = {
    color: Colors.black,
    fontFamily: 'Montserrat-Regular',
    marginBottom: 5,
  };

  const bold = {
    fontFamily: 'Montserrat-Bold',
  };

  const greeText: any = {
    color: Colors.green,
    fontWeight: '700',
  };

  const companyName = company?.user_name;

  const navigation = useNavigation();
  const renderCompanyAvatar = () => {
    const shortNames = !companyName
      ? undefined
      : companyName
          .split(' ')
          .map((name) => name[0])
          .join('')
          .slice(0, 2);
    return (
      <View
        style={{
          width: 70,
          height: 70,
          borderRadius: 35,
          backgroundColor: 'black',
          borderColor: 'white',
          borderWidth: 1,
          marginRight: 15,
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        {!!shortNames ? (
          <Text style={[textStyle, { fontSize: 30, color: 'white' }]}>{shortNames}</Text>
        ) : (
          <Ionicons name="file-tray" color="white" size={30} />
        )}
      </View>
    );
  };

  const renderExpireTag = () =>
    isExpired ? (
      <View style={{ backgroundColor: '#777', borderRadius: 4, paddingHorizontal: 5, paddingVertical: 2, marginRight: 15 }}>
        <CustomText text={'EXPIRED'} style={{ color: '#FFF', fontWeight: '700' }} />
      </View>
    ) : (
      <></>
    );

  const CardWrapper = ({ children }) => {
    if (isFavorite && !isExpired) {
      return <View style={[favoriteListItem, style]}>{children}</View>;
    }
    return <View style={[listItem, style]}>{children}</View>;
  };
  
  return (
    <TouchableOpacity activeOpacity={0.9} onPress={onPress}>
      <CardWrapper>
        {isFavorite && (
          <View style={{ position: 'absolute', top: 8, right: 8, zIndex: 10 }}>
            <FontistoIcons name="favorite" size={28} color={'#3f39e8'} />
          </View>
        )}
        {showAvatar && (
          <View style={{ flexDirection: 'column', alignItems: 'center', gap: 5 }}>
            {renderCompanyAvatar()}
            {renderExpireTag()}
          </View>
        )}

        <View style={{ flex: 1 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <TouchableOpacity onPress={() => navigation.navigate('Profile', { isView: true, userId: company?.ID })}>
              <Text style={[textStyle, bold, isFavorite && { color: '#1a1a1a', fontWeight: '800' }]}>{companyName}</Text>
            </TouchableOpacity>
            <Text style={[textStyle, bold, isFavorite && { color: '#1a1a1a', fontWeight: '800' }]}> - {name}</Text>
          </View>
          {!showAvatar && renderExpireTag()}
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 3 }}>
            <StarRating disabled={true} fullStarColor="#FFD700" maxStars={5} sty rating={reviewedRating} starSize={20} />
            <Text style={isFavorite && { color: '#1a1a1a', fontWeight: '600' }}>({reviewedNumber})</Text>
          </View>
          <Text style={[textStyle]}>{title}</Text>
          <Text style={[textStyle, isGreen ? greeText : {}, isFavorite && !isGreen && { color: '#1a1a1a', fontWeight: '600' }]}>{date}</Text>
          <Text style={[textStyle, isGreen ? greeText : {}, isFavorite && !isGreen && { color: '#1a1a1a', fontWeight: '600' }]}>{location}</Text>
        </View>
      </CardWrapper>
    </TouchableOpacity>
  );
}
