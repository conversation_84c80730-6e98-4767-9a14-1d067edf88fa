import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Alert, Dimensions, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Checkbox from './elements/Checkbox';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { LogoVisa, LogoMastercard } from '../assets/image';
import { useStripe } from '@stripe/stripe-react-native';
import axiosInstance from '../utils/axiosManager';
import CreditWallet from './CreditWallet';
import FAIcon from 'react-native-vector-icons/FontAwesome';

type PaymentStepProps = {
  isStandard: boolean;
  setIsStandard: (value: boolean) => void;
  isPlusPost: boolean;
  setIsPlusPost: (value: boolean) => void;
  handleSubmit: () => void;
  setIsPaymentStep: (value: boolean) => void;
  isLoading: boolean;
  navigation: any;
  route: any;
};
export default function PostJobPaymentStep({
  isStandard,
  setIsStandard,
  isPlusPost,
  setIsPlusPost,
  handleSubmit,
  setIsPaymentStep,
  isLoading,
  navigation,
  route,
}: PaymentStepProps) {
  const [isUseBuyCredit, setIsUseBuyCredit] = useState(false);
  const [balance, setBalance] = useState(0);
  const { initPaymentSheet, presentPaymentSheet } = useStripe();
  const [loading, setLoading] = useState(false);
  const [isLoadingBalance, setIsLoadingBalance] = useState(false);

  const [paymentType, setPaymentType] = useState<'card' | 'credit-wallet' | null>(null);

  const [isHaveAssignOrderNumber, setIsHaveAssignOrderNumber] = useState(false);
  const [orderNumberInvoiceDetail, setOrderNumberInvoiceDetail] = useState(null);

  useEffect(() => {
    if (route.params) {
      setOrderNumberInvoiceDetail(route.params?.orderNumberInvoiceDetail);
      if (route.params?.orderNumberInvoiceDetail?.isHaveAssignOrderNumber && route.params?.orderNumberInvoiceDetail) {
        setIsHaveAssignOrderNumber(true);
        setPaymentType(null);
      }
    }
  }, [route]);

  const handleGetBalance = async () => {
    try {
      setIsLoadingBalance(true);
      const response = await axiosInstance.get('/api/payment/getCurrentAmount');
      console.log('response', response.data);
      setBalance(Number(response.data.data?.metadata?.credits || 0));
    } catch (error) {
      Alert.alert('Error', error.message || 'Something went wrong');
    } finally {
      setIsLoadingBalance(false);
      setIsUseBuyCredit(true);
    }
  };

  const handleCardPayment = async () => {
    try {
      setLoading(true);
      console.log('handleCardPayment');
      const response = await axiosInstance.post('/api/payment/create-payment-intent', {
        amount: isStandard ? 3599 : 4599,
        currency: 'eur',
      });
      console.log('response', response.data);
      const { clientSecret } = response.data;
      console.log('clientSecret', clientSecret);
      const { error: initError } = await initPaymentSheet({
        paymentIntentClientSecret: clientSecret,
        merchantDisplayName: 'Trade motion',
      });
      console.log('initError', initError);
      if (initError) {
        throw initError;
      }
      const { error } = await presentPaymentSheet();
      console.log('error', error);
      if (error) {
        Alert.alert('Payment failed', error.message);
      } else {
        Alert.alert('Success', 'Payment completed successfully and posting job!');
        handleSubmit();
      }
    } catch (error) {
      Alert.alert('Error', error.message || 'Payment failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCreditWalletPayment = async () => {
    try {
      setLoading(true);
      if (balance === 0) {
        Alert.alert('Error', 'No credit available');
        return;
      }
      // reduce balance 1 and update balance
      const newBalance = balance - 1;
      const response = await axiosInstance.post('/api/payment/stripeUpdateDeposit', {
        new_credits: newBalance,
      });
      if (response.status === 200) {
        handleSubmit();
      }
    } catch (error) {
      Alert.alert('Error', error.message || 'Something went wrong');
      console.log('error', error);
    } finally {
      setLoading(false);
      setPaymentType('credit-wallet');
    }
  };

  const handleAssignOrderNumberPayment = async () => {
    try {
      setLoading(true);
      handleSubmit();
    } catch (error) {
      Alert.alert('Error', error.message || 'Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  if (isUseBuyCredit) {
    return (
      <CreditWallet
        balance={balance}
        getBalance={handleGetBalance}
        setIsUseBuyCredit={setIsUseBuyCredit}
        onSaveAndConfirm={() => {
          setIsUseBuyCredit(false);
          setPaymentType('credit-wallet');
        }}
      />
    );
  }

  return (
    <>
      <View>
        <View>
          <View style={styles.title}>
            <Text style={styles.text}>Post options</Text>
          </View>
          <View style={styles.checkboxContainer}>
            <Checkbox
              isChecked={isStandard}
              onPress={() => {
                setIsStandard(true);
                setIsPlusPost(false);
                setIsHaveAssignOrderNumber(false);
                setOrderNumberInvoiceDetail(null);
                setPaymentType(null);
              }}
            />
            <Text>Standard job post (£35.99)</Text>
          </View>
          <View style={styles.checkboxContainer}>
            <Checkbox
              isChecked={isPlusPost}
              onPress={() => {
                setIsPlusPost(true);
                setIsStandard(false);
                setIsHaveAssignOrderNumber(false);
                setOrderNumberInvoiceDetail(null);
                setPaymentType(null);
              }}
            />
            <Text>Standard plus message all relevant trades with instant notification (£45.99)</Text>
          </View>
          <View style={styles.title}>
            <Text style={styles.text}>Payment options</Text>
          </View>
          <View>
            <TouchableOpacity
              disabled={loading || isLoading}
              onPress={() => {
                if (paymentType === 'card') {
                  setPaymentType(null);
                } else {
                  setPaymentType('card');
                  setIsHaveAssignOrderNumber(false);
                }
              }}
              style={{
                ...styles.button,
                backgroundColor: paymentType === 'card' ? Colors.black : Colors.white,
              }}
            >
              {paymentType === 'card' && <FAIcon name="check-circle" size={25} color="#2bbd66" />}
              <Text style={{ ...styles.buttonText, color: paymentType === 'card' ? Colors.white : Colors.black }}>Debit | Credit card</Text>
              <Image source={LogoVisa} style={{ height: 30, width: 40 }} />
              <Image source={LogoMastercard} style={{ height: 30, width: 40 }} />
            </TouchableOpacity>
          </View>
          <View>
            <TouchableOpacity
              style={{
                ...styles.button,
                backgroundColor: paymentType === 'credit-wallet' && !isLoadingBalance ? Colors.black : Colors.white,
                opacity: isPlusPost ? 0.5 : 1,
                backgroundColor: isPlusPost ? '#E0E0E0' : paymentType === 'credit-wallet' && !isLoadingBalance ? Colors.black : Colors.white,
              }}
              disabled={isPlusPost || loading || isLoading}
              onPress={() => {
                if (paymentType === 'credit-wallet') {
                  setPaymentType(null);
                } else {
                  setIsHaveAssignOrderNumber(false);
                  setOrderNumberInvoiceDetail(null);
                  handleGetBalance();
                  setPaymentType('credit-wallet');
                }
              }}
            >
              {paymentType === 'credit-wallet' && !isLoadingBalance && <FAIcon name="check-circle" size={25} color="#2bbd66" />}
              <Text
                style={{
                  ...styles.buttonText,
                  color: isPlusPost
                    ? '#808080' // Gray text when disabled
                    : paymentType === 'credit-wallet' && !isLoadingBalance
                      ? Colors.white
                      : Colors.black,
                }}
              >
                Use | buy credits (standard post only)
              </Text>
              {isLoadingBalance && <ActivityIndicator size="small" color={Colors.black} />}
            </TouchableOpacity>
          </View>
          <View>
            <TouchableOpacity
              style={{ ...styles.button, backgroundColor: isHaveAssignOrderNumber ? Colors.black : Colors.white }}
              onPress={() => {
                if (isHaveAssignOrderNumber) {
                  setIsHaveAssignOrderNumber(false);
                  setOrderNumberInvoiceDetail(null);
                } else {
                  navigation.navigate('AssignNumberOrder', {
                    orderNumberInvoiceDetail,
                    amount: isStandard ? 3599 : 4599,
                    currency: 'eur',
                  });
                }
              }}
            >
              {isHaveAssignOrderNumber && <FAIcon name="check-circle" size={25} color="#2bbd66" />}
              <Text style={{ ...styles.buttonText, color: isHaveAssignOrderNumber ? Colors.white : Colors.black }}>Assign to order number</Text>
            </TouchableOpacity>
          </View>
        </View>
        <View style={{ marginTop: 40 }}>
          {(paymentType === 'card' || paymentType === 'credit-wallet' || isHaveAssignOrderNumber) && (
            <TouchableOpacity
              onPress={() => {
                if (paymentType === 'card') {
                  handleCardPayment();
                } else if (paymentType === 'credit-wallet') {
                  handleCreditWalletPayment();
                } else if (isHaveAssignOrderNumber && paymentType === null) {
                  handleAssignOrderNumberPayment();
                } else {
                  Alert.alert('Error', 'Please select a payment method');
                }
              }}
              disabled={loading || isLoading}
              style={{
                marginTop: 15,
                padding: 10,
                borderWidth: 1,
                borderRadius: 50,
                width: '100%',
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: Colors.black,
              }}
            >
              {loading || isLoading ? (
                <View style={{ flexDirection: 'row', alignItems: 'center', columnGap: 10 }}>
                  <ActivityIndicator size="small" color={Colors.white} />
                  <Text style={{ color: Colors.white }}>Posting job...</Text>
                </View>
              ) : (
                <Text style={{ color: Colors.white }}>Post job</Text>
              )}
            </TouchableOpacity>
          )}
          <TouchableOpacity
            onPress={() => setIsPaymentStep(false)}
            disabled={loading || isLoading}
            style={{
              marginTop: 15,
              padding: 6,
              borderWidth: 1,
              borderRadius: 50,
              width: '100%',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Text>Edit job</Text>
          </TouchableOpacity>
          <TouchableOpacity
            disabled={loading || isLoading}
            style={{
              marginTop: 15,
              padding: 6,
              borderWidth: 1,
              borderRadius: 50,
              width: '100%',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Text>Terms + Conditions</Text>
          </TouchableOpacity>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    fontSize: 16,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  button: {
    marginTop: 15,
    paddingVertical: 7,
    paddingHorizontal: 15,
    borderWidth: 1,
    borderRadius: 50,
    minHeight: 40,
    width: '100%',
    maxWidth: 350,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    columnGap: 10,
  },
  buttonText: {
    textTransform: 'uppercase',
  },
  buttonContainer: {
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    flexDirection: 'row',
  },
  title: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray,
    paddingBottom: 10,
    marginBottom: 5,
    marginTop: 15,
    width: 180,
  },
  checkboxContainer: {
    width: Dimensions.get('window').width - 100,
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 10,
  },
  input: {
    width: '100%',
    height: 35,
    borderWidth: 1,
    borderColor: Colors.gray,
    padding: 5,
    marginTop: 10,
    marginBottom: 15,
    borderRadius: 10,
  },
  cardField: {
    height: 50,
    marginBottom: 20,
    marginTop: 10,
    flexDirection: 'column',
  },
  card: {
    backgroundColor: '#efefefef',
    color: '#000',
  },
});
