import DocumentPicker from 'react-native-document-picker';
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image } from 'react-native';
import Entypo from 'react-native-vector-icons/Entypo';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';

type TFile = {
  fileCopyUri?: string;
  name: string;
  size: number;
  type: string;
  uri: string;
  isLocal?: boolean;
};

const FilePicker = ({ files, onChange, typePicker, label = 'Attach files' }) => {
  const openPicker = () => {
    DocumentPicker.pick({
      allowMultiSelection: true,
      type: typePicker,
    })
      .then((data: Array<TFile>) => {
        console.log(data);
        onChange([...files.filter((file) => file !== ''), ...data.map((v) => ({ ...v, isLocal: true }))]);
      })
      .catch((err) => {
        console.log(err);
      });
  };

  function checkFileType(url: string) {
    const extension = url.split('.').pop()?.toLowerCase();

    switch (extension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return 'image';
      case 'pdf':
        return 'pdf';
      default:
        return 'unknown';
    }
  }

  const renderThumb = (file: TFile) => {
    if (file) {
      if (typeof file === 'string') {
        const type = checkFileType(file);
        return (
          <View style={styles.thumb}>
            {type === 'image' ? (
              <Image source={{ uri: file }} style={{ height: '100%', width: '100%' }} />
            ) : type === 'pdf' ? (
              <FontAwesome5 name="file-pdf" size={16} color="rgba(0,0,0,0.6)" />
            ) : (
              <FontAwesome5 name="file-alt" size={16} color="rgba(0,0,0,0.6)" />
            )}
          </View>
        );
      } else {
        const isImage = file.type.startsWith('image/');
        const isPDF = file.type === 'application/pdf';
        return (
          <View style={styles.thumb}>
            {isImage ? (
              <Image source={{ uri: file.uri }} style={{ height: '100%', width: '100%' }} />
            ) : isPDF ? (
              <FontAwesome5 name="file-pdf" size={16} color="rgba(0,0,0,0.6)" />
            ) : (
              <FontAwesome5 name="file-alt" size={16} color="rgba(0,0,0,0.6)" />
            )}
          </View>
        );
      }
    }
  };
  return (
    <View style={styles.filesContainer}>
      <TouchableOpacity onPress={openPicker} style={styles.uploadButton}>
        <Entypo name="upload" color="black" size={14} />
        <Text style={{ fontSize: 10 }}>{label}</Text>
      </TouchableOpacity>
      {files.map((v) => {
        if (v) {
          if (typeof v === 'string') {
            return (
              <View key={v} style={styles.uploadedFileContainer}>
                {renderThumb(v)}
                <TouchableOpacity
                  style={styles.trashButton}
                  onPress={() => {
                    // remove the file from the list
                    onChange(files.filter((f) => f !== v));
                  }}
                >
                  <FontAwesome5 name="times" size={12} color="rgba(0,0,0,0.9)" />
                </TouchableOpacity>
              </View>
            );
          } else {
            return (
              <View key={v.uri} style={styles.uploadedFileContainer}>
                {renderThumb(v)}
                <Text style={{ fontSize: 10, maxWidth: 200 }}>{v.name}</Text>
                <TouchableOpacity
                  style={styles.trashButton}
                  onPress={() => {
                    // remove the file from the list
                    onChange(files.filter((f) => f.uri !== v.uri));
                  }}
                >
                  <FontAwesome5 name="times" size={12} color="rgba(0,0,0,0.9)" />
                </TouchableOpacity>
              </View>
            );
          }
        }
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  filesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
    alignItems: 'stretch',
  },
  uploadButton: {
    padding: 5,
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: 5,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 5,
    margin: 5,
    height: 30,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  uploadedFileContainer: {
    padding: 5,
    flexDirection: 'row',
    paddingLeft: 40,
    alignItems: 'center',
    columnGap: 5,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 5,
    margin: 5,
    height: 30,
    borderWidth: 0.5,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  trashButton: {
    height: 30,
    padding: 5,
    justifyContent: 'center',
  },
  thumb: {
    backgroundColor: 'rgba(255,255,255,0.8)',
    borderRadius: 5,
    aspectRatio: 1,
    position: 'absolute',
    top: 1,
    left: 1,
    bottom: 1,
    width: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default FilePicker;
