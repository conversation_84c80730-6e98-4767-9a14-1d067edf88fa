import { Dimensions, StyleSheet, Text, TouchableOpacity, View, ActivityIndicator, TextInput, Alert } from 'react-native';
import React, { useEffect, useState } from 'react';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { useStripe } from '@stripe/stripe-react-native';
import axiosInstance from '../utils/axiosManager';
import AntDesign from 'react-native-vector-icons/AntDesign';

export default function CreditWallet({
  onSaveAndConfirm,
  setIsUseBuyCredit,
  balance,
  getBalance,
}: {
  onSaveAndConfirm: () => void;
  balance: number;
  getBalance: () => void;
  setIsUseBuyCredit: (value: boolean) => void;
}) {
  const [tempBalance, setTempBalance] = useState(balance);
  const [isShowBuyCredit, setIsShowBuyCredit] = useState(false);
  const [isUsingCredit, setIsUsingCredit] = useState(false);
  const [paymentSheetLoading, setPaymentSheetLoading] = useState(false);
  const [numberOfCredits, setNumberOfCredits] = useState(0);
  const { initPaymentSheet, presentPaymentSheet } = useStripe();

  const calculateCost = (credits: number): number => {
    const basePrice = 35.99;
    let totalCost = credits * basePrice;
    if (credits > 50) {
      totalCost *= 0.85; // 15% discount
    } else if (credits > 10) {
      totalCost *= 0.9; // 10% discount
    }
    return Number(totalCost.toFixed(2));
  };

  const handleBuyCredit = async () => {
    try {
      setPaymentSheetLoading(true);
      const response = await axiosInstance.post('/api/payment/stripeCreateDeposit', {
        amount: calculateCost(numberOfCredits) * 100,
        currency: 'eur',
      });
      const { clientSecret, paymentIntentId } = response.data;
      if (!clientSecret) {
        throw new Error('No clientSecret in response');
      }
      console.log('clientSecret', clientSecret);
      const { error } = await initPaymentSheet({
        paymentIntentClientSecret: clientSecret,
        merchantDisplayName: 'Trade motion',
      });
      if (error) {
        throw error;
      }
      const result = await presentPaymentSheet();
      if (result.error) {
        throw result.error;
      }

      const newCredits = Number(balance) + Number(numberOfCredits);
      await axiosInstance.post('/api/payment/stripeUpdateDeposit', {
        new_credits: newCredits,
      });
      await getBalance();
      setNumberOfCredits(0);
      setIsShowBuyCredit(false);
    } catch (error) {
      Alert.alert('Error', error.message || 'Failed to process payment');
      console.log('error', error);
    } finally {
      setPaymentSheetLoading(false);
    }
  };

  useEffect(() => {
    setTempBalance(balance);
  }, [balance]);
  return (
    <View style={styles.container}>
      <View style={{ width: '100%', paddingTop: 35 }}>
        <TouchableOpacity
          onPress={() => setIsUseBuyCredit(false)}
          style={{ position: 'absolute', left: 0, top: 0, flexDirection: 'row', alignItems: 'center', columnGap: 10 }}
        >
          <AntDesign name="arrowleft" size={18} color={Colors.black} />
          <Text style={{ color: Colors.black, fontSize: 14, fontWeight: 'bold' }}>Back</Text>
        </TouchableOpacity>
        <View
          style={{
            borderBottomWidth: 1,
            borderBottomColor: Colors.gray,
            marginBottom: 15,
            paddingBottom: 5,
            width: '100%',
            maxWidth: 230,
          }}
        >
          <Text style={styles.title}>Current credit available:</Text>
        </View>
        <Text style={styles.balance}>{tempBalance}</Text>
        <View>
          <View style={{ borderBottomWidth: 1, borderBottomColor: Colors.gray, paddingBottom: 5, width: '100%', maxWidth: 85 }}>
            <Text style={styles.title}>Options:</Text>
          </View>
          <TouchableOpacity
            style={{ ...styles.button, backgroundColor: isUsingCredit ? Colors.black : Colors.white }}
            onPress={() => {
              if (balance === 0) {
                Alert.alert('Error', 'No credit available');
                return;
              }
              setIsUsingCredit(!isUsingCredit);
              setTempBalance(balance - 1);
              if (isShowBuyCredit) {
                setIsShowBuyCredit(false);
              }
              onSaveAndConfirm();
            }}
          >
            <Text style={{ ...styles.buttonText, color: isUsingCredit ? Colors.white : Colors.black }}>Use credit</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={{ ...styles.button, backgroundColor: isShowBuyCredit ? Colors.black : Colors.white }}
            onPress={() => setIsShowBuyCredit(!isShowBuyCredit)}
          >
            <Text style={{ ...styles.buttonText, color: isShowBuyCredit ? Colors.white : Colors.black }}>Buy credit</Text>
          </TouchableOpacity>
        </View>
        {isShowBuyCredit && (
          <View style={{ width: '100%' }}>
            <View style={{ width: '100%', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text>Number of credits to buy</Text>
              <TextInput
                style={styles.input}
                placeholder="Number of credits"
                value={numberOfCredits.toString()}
                onChangeText={(text) => setNumberOfCredits(parseInt(text) || 0)}
                keyboardType="numeric"
              />
            </View>
            <Text>Cost calculated: £{calculateCost(numberOfCredits)}</Text>
            <TouchableOpacity
              disabled={paymentSheetLoading || numberOfCredits === 0}
              style={{ ...styles.buttonConfirm, opacity: paymentSheetLoading || numberOfCredits === 0 ? 0.5 : 1 }}
              onPress={() => handleBuyCredit()}
            >
              {paymentSheetLoading ? (
                <ActivityIndicator size="small" color={Colors.white} />
              ) : (
                <Text style={{ color: Colors.white }}>Buy credit</Text>
              )}
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  backgroundStyle: {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  },
  text: {
    fontSize: 16,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  button: {
    marginTop: 15,
    paddingVertical: 7,
    paddingHorizontal: 15,
    borderWidth: 1,
    borderRadius: 50,
    minHeight: 40,
    width: '100%',
    maxWidth: 350,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    columnGap: 10,
  },
  buttonText: {
    textTransform: 'uppercase',
  },
  buttonConfirm: {
    marginTop: 25,
    textTransform: 'uppercase',
    textAlign: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    color: Colors.white,
    fontWeight: 'bold',
    fontSize: 16,
    paddingVertical: 10,
    backgroundColor: Colors.black,
    borderRadius: 50,
    width: '100%',
    maxWidth: 130,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  checkboxContainer: {
    width: Dimensions.get('window').width - 100,
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 10,
  },
  input: {
    width: '100%',
    maxWidth: 80,
    height: 35,
    borderWidth: 1,
    borderColor: Colors.gray,
    padding: 5,
    marginTop: 10,
    marginBottom: 15,
    borderRadius: 10,
  },
  balance: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 15,
  },
});
