import React, { useEffect, useState } from 'react';
import { ScrollView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { Text, View, Dimensions, ActivityIndicator, ImageBackground, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import Header from './Header';
import CompanyJobsCard from './elements/CompanyJobsCard';
import { BackgroundGradient } from '../assets/image';
import { hideCompletedJobs, useListBusinessCurrentPosts } from '../services/jobsService';
import CustomText from './CustomText';
import moment from 'moment-timezone';
import SelectDropdown from 'react-native-select-dropdown';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { getTradeOptions } from '../constants/tradeOptions';
import CustomButton from './CustomButton';
import Popover from 'react-native-popover-view/dist/Popover';

const tradeOptions = getTradeOptions();
export default function CurrentPost({ navigation }) {
  const [isLoadingHideCompletedJobs, setIsLoadingHideCompletedJobs] = useState(false);
  const [listJobs, setListJobs] = useState([]);
  const [sortTimeBy, setSortTimeBy] = React.useState('Newest');
  const [sortByStatus, setSortByStatus] = React.useState('all');
  const [sortByTrade, setSortByTrade] = React.useState('All');
  const [isFilterVisible, setFilterVisible] = React.useState(false);

  const backgroundStyle: any = {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  };

  const { list, isFetching, reFetch } = useListBusinessCurrentPosts();
  const filterJobs = (list, status) => {
    if (status === 'all') {
      return [...list];
    }
    return [...list].filter((a) => a.job_status.includes(status));
  };

  const sortJobs = (list, sortTimeBy) => {
    const compareFn = sortTimeBy === 'Newest' ? (a, b) => moment(b.date).diff(moment(a.date)) : (a, b) => moment(a.date).diff(moment(b.date));

    return list.sort(compareFn);
  };

  const sortJobsByTrade = (list, trade) => {
    if (trade === 'All') {
      return [...list];
    }
    return [...list].filter((a) => a.trade.includes(trade));
  };

  useEffect(() => {
    if (list) {
      const filteredJobs = filterJobs(list, sortByStatus);
      const sortedJobs = sortJobs(filteredJobs, sortTimeBy);
      const sortedJobsByTrade = sortJobsByTrade(sortedJobs, sortByTrade);
      setListJobs(sortedJobsByTrade);
    }
  }, [sortByStatus, sortTimeBy, sortByTrade, list]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      reFetch();
    });
    return () => {
      unsubscribe();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleHideCompletedJobs = async () => {
    setIsLoadingHideCompletedJobs(true);
    const completedJobs = listJobs.filter((item) => item.job_status === 'Completed');
    if (completedJobs.length > 0) {
      const res = await hideCompletedJobs(completedJobs.map((item) => item.ID));
      if (res.code) {
        Alert.alert('Error', res.message);
      } else {
        Alert.alert('Success', 'Completed jobs hidden successfully');
        reFetch();
      }
    }
    setIsLoadingHideCompletedJobs(false);
  };
  const isHaveCompletedJobs = listJobs.filter((item) => item.job_status.includes('Completed')).length > 0;
  return (
    <SafeAreaView style={backgroundStyle}>
      <ImageBackground source={BackgroundGradient} style={{ ...backgroundStyle, height: Dimensions.get('window').height, paddingBottom: 20 }}>
        <Header navigation={navigation} hasBack title="Active posts" />
        <View style={{ flexDirection: 'row', marginHorizontal: 15, marginTop: 15, justifyContent: 'space-evenly' }}>
          <View>
            <CustomButton
              color="black"
              style={{
                width: Dimensions.get('window').width / 2 - 30,
                backgroundColor: isHaveCompletedJobs ? '#1b53ac' : '#808080',
                borderRadius: 25,
                padding: 15,
                height: 55,
              }}
              disabled={!isHaveCompletedJobs}
              isLoading={isLoadingHideCompletedJobs}
              onPress={handleHideCompletedJobs}
              title="Clear completed jobs"
            />
          </View>

          <View style={{ backgroundColor: Colors.white, borderRadius: 25 }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
              <Popover
                isVisible={isFilterVisible}
                arrowSize={{ width: 0, height: 0 }}
                onRequestClose={() => setFilterVisible(false)}
                popoverStyle={{
                  backgroundColor: Colors.white,
                  borderRadius: 25,
                  padding: 15,
                  width: Dimensions.get('window').width,
                  alignSelf: 'center',
                }}
                from={
                  <TouchableOpacity
                    onPress={() => setFilterVisible(true)}
                    style={{
                      height: 55,
                      flexDirection: 'row',
                      width: Dimensions.get('window').width / 2 - 30,
                      alignSelf: 'center',
                      justifyContent: 'center',
                      alignItems: 'center',
                      padding: 0,
                    }}
                  >
                    <Text style={{ fontSize: 16 }}>Sorting</Text>
                  </TouchableOpacity>
                }
              >
                <SortingComponent
                  setSortTimeBy={setSortTimeBy}
                  sortTimeBy={sortTimeBy}
                  setSortByStatus={setSortByStatus}
                  setSortByTrade={setSortByTrade}
                />
              </Popover>
            </View>
          </View>
        </View>
        <ScrollView style={[styles.container, { marginBottom: 100 }]}>
          <View>
            {isFetching ? (
              <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', height: Dimensions.get('window').height - 400 }}>
                <ActivityIndicator size="large" color={Colors.white} />
              </View>
            ) : (
              <View style={{ paddingTop: 10, gap: 10 }}>
                {listJobs.length === 0 ? (
                  <CustomText style={{ textAlign: 'center', fontSize: 22 }} text={'You have not posted any job'} color="white" />
                ) : (
                  <>
                    {listJobs.map((item, index) => {
                      const numberOfApplication = (item.application || []).length;
                      const jobStatus = item.job_status;
                      const isLeftReviewFromTrade =
                        item?.application && Array.isArray(item.application)
                          ? item.application.some((a) => a.is_left_review_from_trade === 'yes')
                          : false;
                      const userId =
                        item?.application && Array.isArray(item.application)
                          ? item.application.find((a) => a.is_left_review_from_trade === 'yes')?.tradesperson
                          : null;
                      const applicationId =
                        item?.application && Array.isArray(item.application)
                          ? item.application.find((a) => a.is_left_review_from_trade === 'yes')?.ID
                          : null;
                      return (
                        <CompanyJobsCard
                          key={'job-card-' + index}
                          trade={item?.trade}
                          task={item?.task}
                          numberOfApplication={numberOfApplication}
                          jobStatus={jobStatus}
                          // time={`${moment.utc(item.start_date_required).local().format('DD MMM YYYY hh:ssa')}`}
                          contactName={item?.contact_name}
                          siteName={item?.site_name}
                          navigation={navigation}
                          userId={userId}
                          applicationId={applicationId}
                          price={item?.price === 'poa' ? 'POA' : `£${item?.price}`}
                          isLeftReviewFromTrade={isLeftReviewFromTrade}
                          jobId={item?.id}
                          onPress={() =>
                            navigation.navigate('BusinessApplicationsOfAJob', {
                              jobId: item?.id,
                              location: item?.location,
                            })
                          }
                        />
                      );
                    })}
                  </>
                )}
              </View>
            )}
          </View>
        </ScrollView>
      </ImageBackground>
    </SafeAreaView>
  );
}

const SortingComponent = ({ setSortTimeBy, sortTimeBy, setSortByStatus, setSortByTrade }) => {
  const isSmallScreen = Dimensions.get('window').width < 400;

  return (
    <View style={{ backgroundColor: Colors.white, borderRadius: 25 }}>
      <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
        <View style={{ flexDirection: 'column', flex: 1, marginRight: 10 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end' }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
              <CustomText style={{ width: 60 }} text="Sort by: " color="black" />
              <SelectDropdown
                data={[
                  { title: 'Oldest', value: 'Oldest' },
                  { title: 'Newest', value: 'Newest' },
                ]}
                defaultValue={sortTimeBy === 'Oldest' ? { title: 'Oldest', value: 'Oldest' } : { title: 'Newest', value: 'Newest' }}
                onSelect={(selectedItem) => {
                  setSortTimeBy(selectedItem.value);
                }}
                renderButton={(selectedItem, isOpened) => {
                  return (
                    <View style={[styles.dropdownButton, { width: isSmallScreen ? 100 : 120 }]}>
                      <Text style={styles.dropdownButtonTxt}>{selectedItem && selectedItem.title}</Text>
                      <Icon name={isOpened ? 'chevron-up' : 'chevron-down'} style={styles.dropdownButtonArrow} />
                    </View>
                  );
                }}
                renderItem={(item, index, isSelected) => {
                  return (
                    <View style={{ ...styles.dropdownItem, ...(isSelected && { backgroundColor: '#D2D9DF' }) }}>
                      <Text style={styles.dropdownItemTxt}>{item.title}</Text>
                    </View>
                  );
                }}
                showsVerticalScrollIndicator={false}
                dropdownStyle={styles.dropdownMenu}
              />
            </View>
            <View style={{ flexDirection: 'row', alignItems: 'center', alignSelf: 'flex-end' }}>
              <CustomText style={{ width: 60 }} text="Status: " color="black" />
              <SelectDropdown
                data={[
                  { title: 'All', value: 'all' },
                  { title: 'Approved', value: 'Closed' },
                  { title: 'Open', value: 'Open' },
                ]}
                defaultValue={{ title: 'All', value: 'all' }}
                onSelect={(selectedItem) => {
                  setSortByStatus(selectedItem.value);
                }}
                renderButton={(selectedItem, isOpened) => {
                  return (
                    <View style={[styles.dropdownButton, { width: isSmallScreen ? 80 : 100 }]}>
                      <Text style={styles.dropdownButtonTxt}>{selectedItem && selectedItem.title}</Text>
                      <Icon name={isOpened ? 'chevron-up' : 'chevron-down'} style={styles.dropdownButtonArrow} />
                    </View>
                  );
                }}
                renderItem={(item, index, isSelected) => {
                  return (
                    <View style={{ ...styles.dropdownItem, ...(isSelected && { backgroundColor: '#D2D9DF' }), width: 200 }}>
                      <Text style={styles.dropdownItemTxt}>{item.title}</Text>
                    </View>
                  );
                }}
                showsVerticalScrollIndicator={false}
                dropdownStyle={styles.dropdownMenu}
              />
            </View>
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center', alignSelf: 'flex-start', marginTop: 15 }}>
            <CustomText style={{ width: 60 }} text="Trade: " color="black" />
            <SelectDropdown
              data={tradeOptions}
              defaultValue={{ label: 'All', title: 'All', value: 'All' }}
              onSelect={(selectedItem) => {
                setSortByTrade(selectedItem.value);
              }}
              renderButton={(selectedItem, isOpened) => {
                return (
                  <View style={[styles.dropdownButton, { width: Dimensions.get('window').width - 120 }]}>
                    <Text style={styles.dropdownButtonTxt}>{selectedItem && selectedItem.title}</Text>
                    <Icon name={isOpened ? 'chevron-up' : 'chevron-down'} style={styles.dropdownButtonArrow} />
                  </View>
                );
              }}
              renderItem={(item, index, isSelected) => {
                return (
                  <View style={{ ...styles.dropdownItem, ...(isSelected && { backgroundColor: '#D2D9DF' }) }}>
                    <Text style={styles.dropdownItemTxt}>{item.title}</Text>
                  </View>
                );
              }}
              showsVerticalScrollIndicator={false}
              dropdownStyle={styles.dropdownMenu}
            />
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 32,
    paddingHorizontal: 16,
  },
  dropdownButton: {
    width: 120,
    height: 30,
    backgroundColor: '#E9ECEF',
    borderRadius: 12,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  dropdownButtonTxt: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
    color: '#151E26',
  },
  dropdownButtonArrow: {
    fontSize: 20,
  },
  dropdownItem: {
    width: '100%',
    flexDirection: 'row',
    paddingHorizontal: 12,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 8,
  },
  dropdownItemTxt: {
    flex: 1,
    fontSize: 18,
    fontWeight: '500',
    color: '#151E26',
  },
  dropdownMenu: {
    backgroundColor: '#E9ECEF',
    borderRadius: 8,
  },
});
