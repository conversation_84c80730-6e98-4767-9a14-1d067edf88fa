import React, { useEffect } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { View, Dimensions, TouchableOpacity, ActivityIndicator, Text, Linking, ImageBackground } from 'react-native';
import Header from '../components/Header';
import { useListBusinessGetFlash } from '../services/flashService';
import CustomText from '../components/CustomText';
import { useAuth } from '../../context/AuthContext';
import { ScrollView } from 'react-native-gesture-handler';
import { BackgroundGradient } from '../assets/image';

const backgroundStyle = {
  backgroundColor: Colors.black,
  width: '100%',
  height: '100%',
};

const newsForm = {
  backgroundColor: Colors.white,
  marginTop: 0,
  borderRadius: 25,
  flexDirection: 'column',
  gap: 20,
  height: Dimensions.get('window').height - 350,
};

const formRow = {
  borderBottomColor: Colors.gray,
  borderBottomWidth: 1,
  marginBottom: 15,
  paddingBottom: 15,
  gap: 5,
};

export default function BusinessListFlash({ navigation }) {
  const { authState } = useAuth();
  const { user } = authState;
  const styles = require('../../style/style');
  const { isFetching, list, reFetch } = useListBusinessGetFlash(user.id);

  useEffect(() => {
    navigation.addListener('focus', () => {
      reFetch();
    });
  }, []);

  return (
    <SafeAreaView style={backgroundStyle}>
      <ImageBackground source={BackgroundGradient} style={{ ...backgroundStyle, height: Dimensions.get('window').height }}>
        <Header title="Newsflash posted" navigation={navigation} hasBack />
        <View style={styles.container}>
          <View
            style={{
              marginBottom: 15,
              alignSelf: 'center',
              borderRadius: 25,
              width: '100%',
              gap: 5,
              padding: 20,
              backgroundColor: Colors.white,
            }}
          >
            <Text>
              Contact our marketing team to discuss your New flash campaign today! Get your news out to thousands of trades locally or nationwide.
            </Text>
            <Text style={{ flexDirection: 'row' }}>
              Email all enquiries to:{' '}
              <Text style={{ color: 'blue' }} onPress={() => Linking.openURL('mailto:<EMAIL>')}>
                <EMAIL>
              </Text>
            </Text>
          </View>
          {isFetching ? (
            <View style={{ flex: 1, marginTop: 30, justifyContent: 'center', alignItems: 'center' }}>
              <ActivityIndicator size="large" color={Colors.white} />
            </View>
          ) : (
            <>
              <View style={newsForm}>
                <View
                  style={{
                    height: Dimensions.get('window').height - 420,
                  }}
                >
                  <ScrollView>
                    <View
                      style={{
                        padding: 20,
                      }}
                    >
                      {list.length > 0 ? (
                        list.map((news) => (
                          <TouchableOpacity
                            key={news.id}
                            onPress={() => {
                              navigation.navigate('NewsflashDetail', { newsFlashData: news, isEdit: true });
                            }}
                          >
                            <View style={formRow}>
                              <CustomText text={news?.poster[0]?.user_name} isBold={true} />
                              <CustomText text={news?.title_flash} />
                            </View>
                          </TouchableOpacity>
                        ))
                      ) : (
                        <CustomText style={{ textAlign: 'center', fontSize: 22 }} text={'You have no news here'} color="white" />
                      )}
                    </View>
                  </ScrollView>
                </View>
                {/* <TouchableOpacity
                  style={createFlashButton}
                  onPress={() => {
                    navigation.navigate('PostNewsFlash');
                  }}
                >
                  <CustomText text="Create a NewsFlash" isBold={true} color="white" />
                </TouchableOpacity> */}
              </View>
            </>
          )}
        </View>
      </ImageBackground>
      {/* <ScrollView> */}

      {/* </ScrollView> */}
    </SafeAreaView>
  );
}
