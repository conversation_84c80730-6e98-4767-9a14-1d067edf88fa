{"name": "trademotion2", "version": "0.0.112", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "prepare": "husky install", "increase-version": "node increment-version.js && git add package.json"}, "dependencies": {"@pusher/pusher-websocket-react-native": "^1.3.1", "@react-native-async-storage/async-storage": "^1.19.1", "@react-native-community/datetimepicker": "^7.4.1", "@react-native-firebase/app": "19.0.0", "@react-native-firebase/auth": "^19.0.0", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.13", "@stripe/stripe-react-native": "^0.38.6", "axios": "^1.4.0", "deprecated-react-native-prop-types": "^5.0.0", "express": "^4.18.2", "geolib": "^3.3.4", "jotai": "^2.10.1", "lodash": "^4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "react": "18.2.0", "react-hook-form": "^7.50.1", "react-native": "0.72.17", "react-native-add-calendar-event": "^5.0.0", "react-native-bouncy-checkbox": "^3.0.7", "react-native-document-picker": "8.2.1", "react-native-dotenv": "^3.4.11", "react-native-dropdown-select-list": "^2.0.4", "react-native-element-dropdown": "^2.10.2", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.12.1", "react-native-gifted-chat": "^2.4.0", "react-native-image-picker": "^7.1.2", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-keychain": "^10.0.0", "react-native-maps": "^1.11.3", "react-native-modal": "^13.0.1", "react-native-onesignal": "^5.0.0", "react-native-permissions": "^4.1.5", "react-native-popover-view": "^5.1.8", "react-native-safe-area-context": "^4.7.1", "react-native-screens": "^3.23.0", "react-native-select-dropdown": "^4.0.1", "react-native-signature-canvas": "^4.7.2", "react-native-star-rating": "^1.1.0", "react-native-touch-id": "^4.4.1", "react-native-vector-icons": "^10.0.3", "react-native-webview": "^13.8.6", "rn-select-date-range": "^5.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.9", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "husky": "^9.0.11", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.7", "prettier": "^2.4.1", "prettier-airbnb-config": "^1.0.0", "react-test-renderer": "18.2.0", "standard-version": "^9.5.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}, "description": "This is a new [**React Native**](https://reactnative.dev) project, bootstrapped using [`@react-native-community/cli`](https://github.com/react-native-community/cli).", "main": "app.js", "author": "", "license": "ISC"}