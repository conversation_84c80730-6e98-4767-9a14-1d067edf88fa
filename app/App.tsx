/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React, { useEffect } from 'react';
import { Layout } from './Layout';
import { FormProvider, useForm } from 'react-hook-form';
import { AuthProvider } from './context/AuthContext';
import { OneSignal, LogLevel } from 'react-native-onesignal';
import { NavigationContainer } from '@react-navigation/native';
import { handleNotification } from './src/utils/handleNotification';
import { StripeProvider } from '@stripe/stripe-react-native';
import { navigationRef } from './src/utils/navigationRef';

export default function App() {
  const methods = useForm();
  function navigate(name, params) {
    if (navigationRef.isReady()) {
      navigationRef.navigate(name, params);
    }
  }

  useEffect(() => {
    OneSignal.Debug.setLogLevel(LogLevel.Verbose);
    // Initialize OneSignal
    OneSignal.initialize('************************************');

    // Request notification permissions
    OneSignal.Notifications.requestPermission(true);
    const notificationOpenedHandler = (openedEvent) => {
      console.log('OneSignal: notification opened:', JSON.stringify(openedEvent));
      handleNotification(openedEvent, navigate);
    };

    OneSignal.Notifications.addEventListener('click', notificationOpenedHandler);
  }, []);

  return (
    <FormProvider {...methods}>
      <AuthProvider>
        <StripeProvider
          publishableKey={'pk_test_51PCLAgP3GNjLk0k0zRyKHtqfNVs5FDuORcgZ3Z1VN5MfunV7QUxNLgkOGeAEL4iupTaZRrT2DbCzwWB3dju6laJp00FKtERl3K'}
        >
          <NavigationContainer ref={navigationRef}>
            <Layout />
          </NavigationContainer>
        </StripeProvider>
      </AuthProvider>
    </FormProvider>
  );
}
